<?php
include(__DIR__ . '/../confing/common.php');

/**
 * 货源监控脚本 - 智能商品同步系统
 * 
 * 功能特性：
 * 1. 商品上架/下架/价格监控
 * 2. 智能前缀识别与处理
 *    - 按分类分析货源商品共同前缀（90%阈值）
 *    - 按分类分析本地商品前缀模式
 *    - 支持关键词忽略功能
 * 3. 自动分类映射与商品同步
 * 4. 多货源多价格倍率自定义
 * 5. 邮件通知监控记录
 * 6. 增量同步支持
 * 
 * 前缀处理逻辑：
 * - 货源前缀识别：分析货源分类下所有商品的共同前缀
 * - 本地前缀继承：根据已对接商品的命名模式应用前缀
 * - 关键词忽略：避免将商品核心名称误识别为前缀
 * - 智能清理：去除货源前缀，应用本地前缀，保持命名一致性
 * 
 * 使用方法：
 * - 文件放redis文件夹下，挂计划任务设定定时监控即可
 * - 仅监控平台已对接的上游分类，对平台未上架的上游分类自动忽略
 * - 上游只要有getclass接口则均可使用
 * 
 * 作者：FreeDom
 * 版本：2.0
 * 更新：2025年
 */
//-------------------------------------------
//-------------------------------------------
// 脚本配置
$config = [
    'enable_incremental' => false,   // 启用增量同步
    'cache_expire' => 86400,        // 缓存24小时
    'enable_email_notify' => true,  // 邮件通知
    'notify_threshold' => 5,        // 通知阈值
    'batch_size' => 50,            // 批量处理大小
    'show_progress' => true,       // 禁用进度条适合宝塔计划任务
    'verbose_mode' => true,        // 简化输出模式
];

// 价格规则（hids为hid数组，multiplier为价格加价，仅支持倍率加价）
$price_rules = [
    ['hids' => [1], 'multiplier' => 1.05],// 输入符合情况一的hid。价格加价情况一：类似暗网的平台返回的价格，比如源台定价0.2，但是返回的价格是1，即源台单价*5的情况
    ['hids' => [2], 'multiplier' => 1.05 * 5],// 输入符合情况二的hid。价格加价情况二：类似FreeDom平台返回的价格，比如源台定价0.2，返回的价格也是0.2的情况。
    ['hids' => [3], 'multiplier' => 1.1],// 自定义情况
];

// 关键词忽略列表 - 这些关键词不会被识别为前缀的一部分
$ignore_keywords = [
    '学习通', '智慧树'
];

//-------------------------------------------
//-------------------------------------------


// 初始化
$script_start = microtime(true);
$updates = [];
$stats = ['total' => 0, 'success' => 0, 'failed' => 0, 'processed' => 0];

// 检测运行环境
$is_cron = !isset($_SERVER['TERM']) || empty($_SERVER['TERM']);
if ($is_cron) {
    $config['show_progress'] = false;
    $config['verbose_mode'] = false;
}

echo "========================================\n";
echo "货源监控脚本启动\n";
echo "开始时间: " . date('Y-m-d H:i:s') . "\n";
echo "运行环境: " . ($is_cron ? "计划任务" : "交互终端") . "\n";
echo "========================================\n";

// Redis连接
try {
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379);
    echo "redis连接成功\n";
} catch (Exception $e) {
    echo "Redis连接失败使用全量同步\n";
    $config['enable_incremental'] = false;
}

// 邮件模板定义
function getMonitorReportTemplate($data) {
    $subject = "【货源监控】系统监控报告 - " . $data['monitor_time'];

    $body = "
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
            <h2 style='color: #007bff; margin: 0;'>📊 货源监控报告</h2>
            <p style='color: #6c757d; margin: 5px 0 0 0;'>监控时间: {$data['monitor_time']}</p>
        </div>

        <div style='background-color: #fff; padding: 20px; border: 1px solid #dee2e6; border-radius: 8px;'>
            <h3 style='color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px;'>📈 变更统计</h3>

            <div style='display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin: 20px 0;'>
                <div style='background-color: #d4edda; padding: 15px; border-radius: 5px; text-align: center;'>
                    <div style='font-size: 24px; font-weight: bold; color: #155724;'>{$data['create_count']}</div>
                    <div style='color: #155724;'>新增商品</div>
                </div>
                <div style='background-color: #d1ecf1; padding: 15px; border-radius: 5px; text-align: center;'>
                    <div style='font-size: 24px; font-weight: bold; color: #0c5460;'>{$data['online_count']}</div>
                    <div style='color: #0c5460;'>恢复上架</div>
                </div>
                <div style='background-color: #f8d7da; padding: 15px; border-radius: 5px; text-align: center;'>
                    <div style='font-size: 24px; font-weight: bold; color: #721c24;'>{$data['offline_count']}</div>
                    <div style='color: #721c24;'>商品下架</div>
                </div>
                <div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; text-align: center;'>
                    <div style='font-size: 24px; font-weight: bold; color: #856404;'>{$data['update_count']}</div>
                    <div style='color: #856404;'>价格更新</div>
                </div>
            </div>

            <div style='background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                <h4 style='margin: 0 0 10px 0; color: #495057;'>📋 本次监控总结</h4>
                <p style='margin: 5px 0; color: #6c757d;'>• 总变更数量: <strong>{$data['total_changes']}</strong> 个商品</p>
                <p style='margin: 5px 0; color: #6c757d;'>• 重要变更: <strong>" . ($data['create_count'] + $data['online_count'] + $data['offline_count']) . "</strong> 个 (新增/上架/下架)</p>
                <p style='margin: 5px 0; color: #6c757d;'>• 价格调整: <strong>{$data['update_count']}</strong> 个商品</p>
            </div>

            <div style='border-top: 1px solid #dee2e6; padding-top: 15px; margin-top: 20px;'>
                <p style='color: #6c757d; font-size: 14px; margin: 0;'>
                    💡 <strong>提示:</strong> 此报告由货源监控脚本自动生成，当检测到重要变化时会发送通知。
                </p>
            </div>
        </div>

        <footer style='text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6;'>
            <p style='color: #6c757d; font-size: 12px; margin: 0;'>
                系统自动邮件，请勿回复 | 货源监控系统
            </p>
        </footer>
    </div>";

    return [
        'subject' => $subject,
        'body' => $body
    ];
}

// 邮件发送函数
function sendMonitorReportEmail($toEmail, $toName, $data) {
    global $conf;

    // 检查邮件功能是否开启
    if ($conf['mailstatus'] !== "1") {
        echo "邮件功能未开启\n";
        return false;
    }

    try {
        require_once(__DIR__ . '/../confing/my_vendor/vendor/autoload.php');

        $mail = new PHPMailer\PHPMailer\PHPMailer(true);

        // 服务器配置
        $mail->isSMTP();
        $mail->Host = $conf['smtp_server'] ?? '';
        $mail->SMTPAuth = true;
        $mail->Username = $conf['mailusername'] ?? '';
        $mail->Password = $conf['mailpassword'] ?? '';

        // 设置加密方式和端口
        $encryption = strtoupper($conf['encryption'] ?? 'TLS');
        $port = intval($conf['smtp_port'] ?? 587);

        if ($encryption === 'SSL') {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
            $port = $port ?: 465;
        } else {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
            $port = $port ?: 587;
        }

        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';

        // 发件人
        $fromEmail = $conf['from_email'] ?? $conf['mailusername'] ?? '';
        $fromName = $conf['from_name'] ?? '货源监控系统';
        $mail->setFrom($fromEmail, $fromName);

        // 收件人
        $mail->addAddress($toEmail, $toName);

        // 获取邮件模板
        $template = getMonitorReportTemplate($data);

        // 邮件内容
        $mail->isHTML(true);
        $mail->Subject = $template['subject'];
        $mail->Body = $template['body'];
        $mail->AltBody = strip_tags($template['body']);

        $result = $mail->send();

        if ($result) {
            echo "监控报告邮件发送成功: {$toEmail}\n";
        } else {
            echo "监控报告邮件发送失败: {$toEmail}\n";
        }

        return $result;

    } catch (Exception $e) {
        echo "邮件发送异常: " . $e->getMessage() . "\n";
        return false;
    }
}

// 辅助函数
function getProductFingerprint($product) {
    return md5($product['price'] . '|' . $product['status'] . '|' . $product['content']);
}

function getCachedData($redis, $hid) {
    if (!$redis) return null;
    $cached = $redis->get("supplier_data_{$hid}");
    return $cached ? json_decode($cached, true) : null;
}

function setCachedData($redis, $hid, $data) {
    if (!$redis) return false;
    return $redis->setex("supplier_data_{$hid}", 86400, json_encode($data));
}

function applyPriceRule($hid, $price, $rules) {
    foreach ($rules as $rule) {
        if (in_array($hid, $rule['hids'])) {
            return $price * $rule['multiplier'];
        }
    }
    return $price;
}

// 分析已对接商品的前缀和分类模式
function analyzeExistingProducts($local_products, $local_fenlei) {
    $patterns = [];

    foreach ($local_products as $cid => $product) {
        $local_name = $product['name'];
        $product_fenlei = $product['fenlei'];
        $sort = $product['sort'] ?? 0;

        // 只分析指定本地分类的商品
        if ($product_fenlei != $local_fenlei) continue;

        // 存储模式信息
        if (!isset($patterns[$product_fenlei])) {
            $patterns[$product_fenlei] = [
                'count' => 0,
                'max_sort' => 0,
                'prefixes' => [],
                'sample_names' => []
            ];
        }

        $patterns[$product_fenlei]['count']++;
        $patterns[$product_fenlei]['max_sort'] = max($patterns[$product_fenlei]['max_sort'], $sort);
        $patterns[$product_fenlei]['sample_names'][] = $local_name;
    }

    if (empty($patterns)) return null;

    $target_fenlei = array_keys($patterns)[0];
    
    // 分析前缀模式
    $sample_names = $patterns[$target_fenlei]['sample_names'];
    $common_prefix = extractCommonPrefix($sample_names);
    
    // 分析本地商品是否没有前缀（即商品名称本身就是核心名称）
    $has_prefix = !empty($common_prefix);
    $prefix_type = $has_prefix ? 'with_prefix' : 'no_prefix';

    return [
        'target_fenlei' => $target_fenlei,
        'common_prefix' => $common_prefix,
        'max_sort' => $patterns[$target_fenlei]['max_sort'],
        'product_count' => $patterns[$target_fenlei]['count'],
        'prefix_type' => $prefix_type,
        'sample_names' => $sample_names
    ];
}

// 按分类分析货源商品的前缀模式
function analyzeSourceProductsByCategory($products, $fenlei) {
    if (empty($products)) return null;
    
    // 获取该分类下的所有商品名称
    $category_products = [];
    foreach ($products as $cid => $product) {
        if ($product['fenlei'] == $fenlei) {
            $category_products[] = $product['name'];
        }
    }
    
    if (empty($category_products)) return null;
    
    // 分析该分类下的公共前缀
    $common_prefix = extractCommonPrefix($category_products);
    
    return [
        'fenlei' => $fenlei,
        'common_prefix' => $common_prefix,
        'product_count' => count($category_products),
        'sample_names' => $category_products
    ];
}

// 提取公共前缀 - 从左到右逐字符比较所有商品的共同前缀，支持关键词忽略
function extractCommonPrefix($names) {
    global $ignore_keywords;
    
    if (empty($names)) return '';

    // 找到最短的商品名称长度作为比较基准
    $min_length = PHP_INT_MAX;
    foreach ($names as $name) {
        $length = mb_strlen($name, 'UTF-8');
        if ($length < $min_length) {
            $min_length = $length;
        }
    }

    if ($min_length == 0) return '';

    $common_prefix = '';
    $total_names = count($names);
    $threshold = $total_names * 0.9; // 90%阈值

    // 从左到右逐字符比较
    for ($pos = 0; $pos < $min_length; $pos++) {
        $current_char = mb_substr($names[0], $pos, 1, 'UTF-8');
        $match_count = 0;

        // 检查所有商品在该位置是否有相同的字符
        foreach ($names as $name) {
            $char_at_pos = mb_substr($name, $pos, 1, 'UTF-8');
            if ($char_at_pos === $current_char) {
                $match_count++;
            }
        }

        // 如果该位置的字符匹配率超过90%，则认为是共同前缀的一部分
        if ($match_count >= $threshold) {
            $common_prefix .= $current_char;
        } else {
            // 一旦遇到不匹配的字符，停止比较
            break;
        }
    }

    // 关键词忽略处理
    if (!empty($common_prefix)) {
        $cleaned_prefix = $common_prefix;
        
        // 检查前缀中是否包含需要忽略的关键词
        foreach ($ignore_keywords as $keyword) {
            if (mb_strpos($cleaned_prefix, $keyword, 0, 'UTF-8') !== false) {
                // 找到关键词的位置，截取关键词之前的部分作为前缀
                $keyword_pos = mb_strpos($cleaned_prefix, $keyword, 0, 'UTF-8');
                $cleaned_prefix = mb_substr($cleaned_prefix, 0, $keyword_pos, 'UTF-8');
                break; // 只处理第一个匹配的关键词
            }
        }
        
        // 清理前缀末尾的空格，但保留连字符
        $cleaned_prefix = rtrim($cleaned_prefix, ' ');
        
        return $cleaned_prefix;
    }

    return $common_prefix;
}

// 移除货源商品的原有前缀（保留作为备用方法）
function removeSourcePrefix($product_name) {
    // 这个方法保留作为备用，主要使用按分类的前缀移除方法
    return $product_name;
}

// 按分类移除货源商品的原有前缀
function removeSourcePrefixByCategory($product_name, $category_prefix) {
    if (empty($category_prefix)) {
        return removeSourcePrefix($product_name);
    }
    
    $cleaned_name = $product_name;
    
    // 如果商品名称以分类前缀开头，直接移除
    if (mb_strpos($cleaned_name, $category_prefix, 0, 'UTF-8') === 0) {
        $cleaned_name = mb_substr($cleaned_name, mb_strlen($category_prefix, 'UTF-8'), null, 'UTF-8');
        $cleaned_name = ltrim($cleaned_name, '-_ ');
        return $cleaned_name;
    }
    
    // 否则使用通用前缀移除逻辑
    return removeSourcePrefix($product_name);
}

// 进度条显示函数
function showProgress($current, $total, $prefix = '', $width = 50) {
    global $config;

    // 如果禁用进度条只在完成时显示简单信息
    if (!$config['show_progress']) {
        if ($current >= $total) {
            echo " {$prefix}: {$total} 项处理完成\n";
        }
        return;
    }

    $percent = $total > 0 ? ($current / $total) * 100 : 0;
    $filled = intval($width * $current / max($total, 1));
    $empty = $width - $filled;

    $bar = str_repeat('', $filled) . str_repeat('', $empty);
    $progress = sprintf("\r%s [%s] %d/%d (%.1f%%)", $prefix, $bar, $current, $total, $percent);

    echo $progress;
    if ($current >= $total) {
        echo "\n";
    }
    flush();
}

// 获取需要监控的货源商
$check_hids = array_merge(...array_column($price_rules, 'hids'));
$hids_str = implode(',', $check_hids);

// 批量加载本地商品
echo "加载本地商品数据...\n";
$localProducts = [];
$result = $DB->query("SELECT cid, noun, name, status, docking, fenlei, sort FROM qingka_wangke_class WHERE docking IN ({$hids_str})");
while ($row = $DB->fetch($result)) {
    $localProducts[$row['docking']][$row['noun']] = $row;
}
echo "已加载 " . count($localProducts) . " 个货源商数据\n";

// 处理每个货源商
$suppliers = $DB->query("SELECT * FROM qingka_wangke_huoyuan WHERE hid IN ({$hids_str}) AND status=1");
$suppliers_list = [];
while ($supplier = $DB->fetch($suppliers)) {
    $suppliers_list[] = $supplier;
}

$total_suppliers = count($suppliers_list);
echo "\n开始处理 {$total_suppliers} 个货源商\n";

foreach ($suppliers_list as $index => $supplier) {
    $hid = $supplier['hid'];
    $stats['total']++;
    $current_supplier = $index + 1;

    // 显示货源商进度
    showProgress($current_supplier, $total_suppliers, " 货源商进度", 30);
    echo "处理: {$supplier['name']} (HID: {$hid})\n";
    
    // API调用
    $api_data = array("uid" => $supplier["user"], "key" => $supplier["pass"]);
    $api_result = get_url("{$supplier['url']}/api.php?act=getclass", $api_data);
    
    if (!$api_result || !($result = json_decode($api_result, true)) || !isset($result["data"])) {
        echo "API调用失败\n";
        $stats['failed']++;
        continue;
    }
    
    $data = $result["data"];
    $local = $localProducts[$hid] ?? [];
    $stats['success']++;
    
    // 增量同步检查
    if ($config['enable_incremental']) {
        $cached = getCachedData($redis, $hid);
        if ($cached) {
            $current_fps = [];
            foreach ($data as $product) {
                $current_fps[$product['cid']] = getProductFingerprint($product);
            }
            
            $cached_fps = $cached['fingerprints'] ?? [];
            $changed = [];
            
            foreach ($data as $product) {
                $cid = $product['cid'];
                if (!isset($cached_fps[$cid]) || $cached_fps[$cid] !== $current_fps[$cid]) {
                    $changed[] = $product;
                }
            }
            
            // 检查删除的商品
            foreach ($cached_fps as $cid => $fp) {
                if (!isset($current_fps[$cid]) && isset($local[$cid]) && $local[$cid]['status'] == 1) {
                    $updates[] = ['docking' => $hid, 'noun' => $cid, 'status' => 0, 'action' => 'offline'];
                }
            }
            
            $data = $changed;
            echo "增量同步: 处理 " . count($data) . " 个变化商品\n";
        }
        
        // 更新缓存
        $cache_data = ['timestamp' => time(), 'fingerprints' => []];
        foreach ($result["data"] as $product) {
            $cache_data['fingerprints'][$product['cid']] = getProductFingerprint($product);
        }
        setCachedData($redis, $hid, $cache_data);
    }
    
    if (empty($data)) {
        echo "无变化\n";
        continue;
    }
    
    // 按分类分组
    $by_fenlei = [];
    foreach ($data as $product) {
        $by_fenlei[$product['fenlei']][$product['cid']] = $product;
    }
    
    $update_count = $online_count = $offline_count = 0;
    
    // 处理每个分类
    foreach ($by_fenlei as $fenlei => $products) {
        // 检查是否已对接此分类 - 查找该货源分类下是否有已对接的商品
        $has_integrated = false;
        $local_fenlei_mapping = []; // 货源分类ID => 本地分类ID的映射
        
        foreach ($products as $cid => $product) {
            if (isset($local[$cid])) {
                $has_integrated = true;
                $local_fenlei_mapping[$fenlei] = $local[$cid]['fenlei'];
                break;
            }
        }
        
        if (!$has_integrated) {
            echo "   跳过未对接分类: {$fenlei}\n";
            continue;
        }
        
        echo "   监控分类: {$fenlei} → 本地分类: {$local_fenlei_mapping[$fenlei]}\n";

        // 首先分析货源商品在该分类下的前缀模式
        $source_pattern = analyzeSourceProductsByCategory($products, $fenlei);
        if ($source_pattern && !empty($source_pattern['common_prefix'])) {
            echo "   检测到货源前缀: '{$source_pattern['common_prefix']}' (匹配率: " . 
                 round(($source_pattern['product_count'] / count($products)) * 100, 1) . "%)\n";
        }

        // 分析本地已对接商品的模式前缀分类排序 - 使用本地分类ID
        $local_fenlei = $local_fenlei_mapping[$fenlei];
        $pattern = analyzeExistingProducts($local, $local_fenlei);
        
        if ($pattern) {
            if ($pattern['prefix_type'] == 'with_prefix') {
                echo "   本地分类前缀: '{$pattern['common_prefix']}' (有前缀模式)\n";
            } else {
                echo "   本地分类: 无前缀模式 (商品名称直接使用)\n";
            }
        }

        // 处理商品
        $total_products = count($products);
        $processed_products = 0;

        // 记录商品数量变化
        $cache_key = "supplier_product_count_{$hid}";
        $last_count = $config['enable_incremental'] && isset($redis) ? $redis->get($cache_key) : 0;
        if ($last_count && $last_count != $total_products) {
            $change = $total_products - $last_count;
            echo " 商品数量变化: {$last_count}  {$total_products} (" . ($change > 0 ? "+{$change}" : $change) . ")\n";
        }
        if ($config['enable_incremental'] && isset($redis)) {
            $redis->setex($cache_key, $config['cache_expire'], $total_products);
        }

        foreach ($products as $cid => $product) {
            $processed_products++;
            if ($config['show_progress'] && $total_products > 10) { // 只有商品数量较多时才显示进度
                showProgress($processed_products, $total_products, "    商品处理", 20);
            }
            $price = applyPriceRule($hid, $product['price'], $price_rules);

            if (!isset($local[$cid])) {
                // 新商品应用继承规则
                if ($pattern) {
                    $original_name = $product['name'] ?? "新商品_{$cid}";
                    // 优先使用货源分类前缀清理，如果没有则使用通用清理
                    $category_prefix = $source_pattern['common_prefix'] ?? '';
                    $cleaned_name = removeSourcePrefixByCategory($original_name, $category_prefix);
                    
                    // 根据本地分类的前缀类型处理
                    if ($pattern['prefix_type'] == 'with_prefix') {
                        // 本地分类有前缀，应用本地前缀
                        $new_name = $pattern['common_prefix'] . $cleaned_name;
                    } else {
                        // 本地分类没有前缀，直接使用清理后的名称
                        $new_name = $cleaned_name;
                    }
                    
                    $target_fenlei = $local_fenlei; // 使用映射的本地分类ID
                    $new_sort = $pattern['max_sort'] + 1;

                    $updates[] = [
                        'price' => $price, 'content' => $product['content'],
                        'docking' => $hid, 'noun' => $cid, 'status' => 1,
                        'action' => 'create_and_online',
                        'name' => $new_name,
                        'fenlei' => $target_fenlei,
                        'sort' => $new_sort,
                        'kcid' => $product['kcid'] ?? ''
                    ];

                    // 更新模式中的最大sort避免重复
                    $pattern['max_sort'] = $new_sort;

                    if (!$config['verbose_mode'] && $total_products <= 10) { // 商品少时显示详细信息
                        echo "     新增商品: {$original_name}  {$new_name} (分类: {$target_fenlei}, sort: {$new_sort})\n";
                        if ($cleaned_name !== $original_name) {
                            echo "       清理前缀: {$original_name}  {$cleaned_name}\n";
                        }
                    }
                } else {
                    // 如果没有找到模式使用默认值
                    $updates[] = [
                        'price' => $price, 'content' => $product['content'],
                        'docking' => $hid, 'noun' => $cid, 'status' => 1,
                        'action' => 'create_and_online',
                        'name' => $product['name'] ?? "新商品_{$cid}",
                        'fenlei' => $local_fenlei, // 使用映射的本地分类ID
                        'sort' => 0,
                        'kcid' => $product['kcid'] ?? ''
                    ];
                    echo "     新增商品: {$product['name']} (ID: {$cid}) - 使用默认设置\n";
                }
                $online_count++;
            } else {
                $localProduct = $local[$cid];

                if ($localProduct['status'] == 0) {
                    // 已存在商品恢复上架
                    $updates[] = [
                        'price' => $price, 'content' => $product['content'],
                        'docking' => $hid, 'noun' => $cid, 'status' => 1, 'action' => 'online'
                    ];
                    $online_count++;
                    if (!$config['verbose_mode'] && $total_products <= 10) {
                        echo "     恢复商品: {$localProduct['name']} (ID: {$cid})\n";
                    }
                } else {
                    // 已存在商品更新价格和内容
                    $updates[] = [
                        'price' => $price, 'content' => $product['content'],
                        'docking' => $hid, 'noun' => $cid, 'status' => 1, 'action' => 'update'
                    ];
                    $update_count++;
                }
            }
        }
        
        // 检查下架商品
        $api_cids = array_keys($products);
        foreach ($local as $cid => $localProduct) {
            if (!in_array($cid, $api_cids) && $localProduct['status'] == 1) {
                $found_in_api = false;
                foreach ($data as $check) {
                    if ($check['cid'] == $cid) {
                        $found_in_api = true;
                        break;
                    }
                }
                if (!$found_in_api) {
                    $updates[] = ['docking' => $hid, 'noun' => $cid, 'status' => 0, 'action' => 'offline'];
                    $offline_count++;
                }
            }
        }
    }
    
    if ($update_count || $online_count || $offline_count) {
        $create_count = count(array_filter($updates, fn($u) => $u['action'] == 'create_and_online' && $u['docking'] == $hid));
        echo "   更新:{$update_count} 上架:" . ($online_count - $create_count) . " 下架:{$offline_count} 新增:{$create_count}\n";
        $stats['processed']++;
    }
}

// 批量更新数据库
if (!empty($updates)) {
    echo "\n 批量更新数据库...\n";

    $by_action = ['online' => [], 'offline' => [], 'update' => [], 'create_and_online' => []];
    foreach ($updates as $update) {
        $by_action[$update['action']][] = $update;
    }

    // 批量上架
    if (!empty($by_action['online'])) {
        $batches = array_chunk($by_action['online'], $config['batch_size']);
        $total_batches = count($batches);
        echo " 批量上架处理中...\n";

        foreach ($batches as $batch_index => $batch) {
            showProgress($batch_index + 1, $total_batches, "上架进度", 25);
            $cases_price = $cases_content = $cases_queryplat = $cases_getnoun = $nouns = [];
            foreach ($batch as $update) {
                $noun = $DB->escape($update['noun']);
                $price = $DB->escape($update['price']);
                $content = $DB->escape($update['content']);
                $docking = $update['docking'];

                $cases_price[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$price}'";
                $cases_content[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$content}'";
                $cases_queryplat[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$docking}'";
                $cases_getnoun[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$noun}'";
                $nouns[] = "'{$noun}'";
            }

            $sql = "UPDATE qingka_wangke_class SET
                    price = CASE " . implode(' ', $cases_price) . " ELSE price END,
                    content = CASE " . implode(' ', $cases_content) . " ELSE content END,
                    queryplat = CASE " . implode(' ', $cases_queryplat) . " ELSE queryplat END,
                    getnoun = CASE " . implode(' ', $cases_getnoun) . " ELSE getnoun END,
                    status = 1
                    WHERE noun IN (" . implode(',', array_unique($nouns)) . ")";
            $DB->query($sql);
        }
        echo " 上架: " . count($by_action['online']) . " 个\n";
    }

    // 批量创建新商品
    if (!empty($by_action['create_and_online'])) {
        $batches = array_chunk($by_action['create_and_online'], $config['batch_size']);
        $total_batches = count($batches);
        echo " 批量创建新商品中...\n";

        foreach ($batches as $batch_index => $batch) {
            showProgress($batch_index + 1, $total_batches, "创建进度", 25);
            $values = [];
            foreach ($batch as $update) {
                $noun = $DB->escape($update['noun']);
                $name = $DB->escape($update['name']);
                $price = $DB->escape($update['price']);
                $content = $DB->escape($update['content']);
                $docking = $update['docking'];
                $fenlei = $DB->escape($update['fenlei']);
                $sort = $update['sort'];
                $kcid = $DB->escape($update['kcid']);

                $values[] = "('{$noun}', '{$name}', '{$price}', '{$content}', '{$docking}', '{$fenlei}', {$sort}, '{$kcid}', 1, '" . date('Y-m-d H:i:s') . "', '{$noun}', '{$docking}')";
            }

            if (!empty($values)) {
                $sql = "INSERT INTO qingka_wangke_class
                        (noun, name, price, content, docking, fenlei, sort, kcid, status, addtime, getnoun, queryplat)
                        VALUES " . implode(',', $values) . "
                        ON DUPLICATE KEY UPDATE
                        name = VALUES(name),
                        price = VALUES(price),
                        content = VALUES(content),
                        fenlei = VALUES(fenlei),
                        sort = VALUES(sort),
                        status = VALUES(status),
                        getnoun = VALUES(getnoun),
                        queryplat = VALUES(queryplat)";
                $DB->query($sql);
            }
        }
        echo " 新增: " . count($by_action['create_and_online']) . " 个\n";
    }

    // 批量下架
    if (!empty($by_action['offline'])) {
        $batches = array_chunk($by_action['offline'], 100);
        $total_batches = count($batches);
        echo " 批量下架处理中...\n";

        foreach ($batches as $batch_index => $batch) {
            showProgress($batch_index + 1, $total_batches, "下架进度", 25);
            $conditions = [];
            foreach ($batch as $update) {
                $noun = $DB->escape($update['noun']);
                $docking = $update['docking'];
                $conditions[] = "(noun = '{$noun}' AND docking = '{$docking}')";
            }
            $sql = "UPDATE qingka_wangke_class SET status = 0 WHERE " . implode(' OR ', $conditions);
            $DB->query($sql);
        }
        echo " 下架: " . count($by_action['offline']) . " 个\n";
    }

    // 批量更新
    if (!empty($by_action['update'])) {
        $batches = array_chunk($by_action['update'], $config['batch_size']);
        $total_batches = count($batches);
        echo " 批量更新价格中...\n";

        foreach ($batches as $batch_index => $batch) {
            showProgress($batch_index + 1, $total_batches, "更新进度", 25);
            $cases_price = $cases_content = $cases_queryplat = $cases_getnoun = $nouns = [];
            foreach ($batch as $update) {
                $noun = $DB->escape($update['noun']);
                $price = $DB->escape($update['price']);
                $content = $DB->escape($update['content']);
                $docking = $update['docking'];

                $cases_price[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$price}'";
                $cases_content[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$content}'";
                $cases_queryplat[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$docking}'";
                $cases_getnoun[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$noun}'";
                $nouns[] = "'{$noun}'";
            }

            $sql = "UPDATE qingka_wangke_class SET
                    price = CASE " . implode(' ', $cases_price) . " ELSE price END,
                    content = CASE " . implode(' ', $cases_content) . " ELSE content END,
                    queryplat = CASE " . implode(' ', $cases_queryplat) . " ELSE queryplat END,
                    getnoun = CASE " . implode(' ', $cases_getnoun) . " ELSE getnoun END
                    WHERE noun IN (" . implode(',', array_unique($nouns)) . ") AND status = 1";
            $DB->query($sql);
        }
        echo " 更新: " . count($by_action['update']) . " 个\n";
    }

    // 记录日志和邮件通知
    $total_changes = count($by_action['online']) + count($by_action['offline']) + count($by_action['create_and_online']);
    $total_operations = $total_changes + count($by_action['update']);

    if ($total_operations > 0) {
        $log_msg = "自动监控: 上架" . count($by_action['online']) . "个, 下架" . count($by_action['offline']) . "个, 新增" . count($by_action['create_and_online']) . "个, 更新" . count($by_action['update']) . "个";
        $DB->query("INSERT INTO qingka_wangke_log (uid, type, text, money, addtime, ip) VALUES (1, '系统监控', '{$log_msg}', 0, '" . date('Y-m-d H:i:s') . "', '127.0.0.1')");

        // 邮件通知 - 只在商品上架/下架/新增时发送更新操作不发送邮件
        if ($config['enable_email_notify'] && ($total_changes >= $config['notify_threshold'])) {
            echo "检测到重要变化 (上架/下架/新增: {$total_changes}个)准备发送邮件通知\n";
            $admin = $DB->get_row("SELECT email, notify FROM qingka_wangke_user WHERE uid='1'");
            if (!empty($admin['email'])) {
                $monitor_data = [
                    'online_count' => count($by_action['online']),
                    'offline_count' => count($by_action['offline']),
                    'create_count' => count($by_action['create_and_online']),
                    'update_count' => count($by_action['update']),
                    'monitor_time' => date('Y-m-d H:i:s'),
                    'total_changes' => $total_changes
                ];

                // 直接发送邮件
                $email_result = sendMonitorReportEmail($admin['email'], '管理员', $monitor_data);
                if ($email_result) {
                    echo "邮件通知发送成功 (上架/下架/新增操作)\n";
                } else {
                    echo "邮件通知发送失败\n";
                }
            }
        } else if ($config['enable_email_notify'] && $total_changes > 0 && $total_changes < $config['notify_threshold']) {
            echo "变化数量 ({$total_changes}个) 未达到通知阈值 ({$config['notify_threshold']}个)不发送邮件\n";
        } else if ($config['enable_email_notify'] && $total_changes == 0 && count($by_action['update']) > 0) {
            echo "仅有价格更新操作 (" . count($by_action['update']) . "个)不发送邮件通知\n";
        }
    }
}

// 最终报告
$execution_time = round(microtime(true) - $script_start, 2);
$memory_peak = round(memory_get_peak_usage(true) / 1024 / 1024, 2);

echo "\n 执行完成\n";
echo " 耗时: {$execution_time}秒 |  内存: {$memory_peak}MB\n";
echo " 货源商: {$stats['success']}/{$stats['total']} 成功 | 处理: {$stats['processed']} 个\n";

if (!empty($updates)) {
    $total_updates = count(array_filter($updates, fn($u) => $u['action'] == 'update'));
    $total_online = count(array_filter($updates, fn($u) => $u['action'] == 'online'));
    $total_offline = count(array_filter($updates, fn($u) => $u['action'] == 'offline'));
    $total_create = count(array_filter($updates, fn($u) => $u['action'] == 'create_and_online'));
    echo " 操作: 更新{$total_updates} 上架{$total_online} 下架{$total_offline} 新增{$total_create}\n";
}

// 清理
if (isset($redis)) $redis->close();
unset($updates, $localProducts);
gc_collect_cycles();

echo "========================================\n";
?>

